import {ClearOutlined, PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {createStyles} from "antd-style";
import {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import React, {memo, useCallback, useMemo, useRef, useState} from "react";
import {ModalChiTietLoaiHoGiaDinh} from "./Component";
import {IModalChiTietLoaiHoGiaDinhRef} from "./Component/Constant";
import {FormTimKiemQuanLyLoaiHoGiaDinh, radioItemTrangThaiLoaiHoGiaDinhSelect, radioItemTrangThaiLoaiHoGiaDinhTable, tableLoaiHoGiaDinhColumn, TableLoaiHoGiaDinhColumnDataIndex, TableLoaiHoGiaDinhColumnDataType} from "./index.configs";
import {useQuanLyLoaiHoGiaDinhContext} from "./index.context";
import "./index.default.scss";

const {ma,ten,trang_thai} = FormTimKiemQuanLyLoaiHoGiaDinh;

const QuanLyLoaiHoGiaDinhContent: React.FC = memo(() => {
  const {listLoaiHoGiaDinh, loading, tongSoDong, filterParams, getChiTietLoaiHoGiaDinh, setFilterParams} = useQuanLyLoaiHoGiaDinhContext();
  const refModalChiTietLoaiHoGiaDinh = useRef<IModalChiTietLoaiHoGiaDinhRef>(null);
  const refSearchInputTable = useRef<InputRef>(null);
  const [searchTextTable, setSearchTextTable] = useState<string>(""); //lưu textsearch để hiển thị vào cell
  const [searchedColumn, setSearchedColumn] = useState<TableLoaiHoGiaDinhColumnDataIndex | "">(""); //key column đang được search

  const dataTableListLoaiHoGiaDinh = useMemo<Array<TableLoaiHoGiaDinhColumnDataType>>(() => {
    try {
      const tableData = listLoaiHoGiaDinh.map((itemLoaiHoGiaDinh, index) => {
        return {
          key: itemLoaiHoGiaDinh.ma || `row-${index}`, // Đảm bảo key luôn là string
          sott: itemLoaiHoGiaDinh.sott,
          ma: itemLoaiHoGiaDinh.ma,
          ten: itemLoaiHoGiaDinh.ten,
          trang_thai: itemLoaiHoGiaDinh.trang_thai,
          ngay_tao: itemLoaiHoGiaDinh.ngay_tao,
          nguoi_tao: itemLoaiHoGiaDinh.nguoi_tao,
          ngay_cap_nhat: itemLoaiHoGiaDinh.ngay_cap_nhat,
          nguoi_cap_nhat: itemLoaiHoGiaDinh.nguoi_cap_nhat,
          trang_thai_ten: itemLoaiHoGiaDinh.trang_thai_ten,
        };
      });
      
      const arrEmptyRow: Array<TableLoaiHoGiaDinhColumnDataType> = fillRowTableEmpty(tableData.length, defaultPaginationTableProps.defaultPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListLoaiHoGiaDinh error", error);
      return [];
    }
  }, [listLoaiHoGiaDinh]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: TableLoaiHoGiaDinhColumnDataIndex) => {
    confirm();
    setSearchTextTable(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: TableLoaiHoGiaDinhColumnDataIndex) => {
      clearFilters();
      setSearchTextTable("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );
  // chức năng search
  const onSearchApi = (values: ReactQuery.ITimKiemPhanTrangLoaiHoGiaDinhParams) => {
    setFilterParams({...filterParams, ...values, trang: 1, so_dong: defaultPaginationTableProps.defaultPageSize});
  };

  // RENDER
  const renderFormInputColum = (props: IFormInput, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  const renderHeaderTableQuanLyLoaiHoGiaDinh = () => {
    return (
      <>
        <Form initialValues={{}} layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={onSearchApi}>
          <Row gutter={16} align={"bottom"}>
            {renderFormInputColum(ma)}
            {renderFormInputColum(ten)}
            {renderFormInputColum({...trang_thai, options: radioItemTrangThaiLoaiHoGiaDinhSelect})}
            <Col span={3}>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
                Tìm kiếm
              </Button>
            </Col>
            <Col span={3}>
              <Button
                type="primary"
                block
                icon={<PlusCircleOutlined />}
                onClick={() => {
                  refModalChiTietLoaiHoGiaDinh.current?.open();
                }}>
                Tạo mới
              </Button>
            </Col>
          </Row>
        </Form>
      </>
    );
  };

  // dataIndex : là các key của column, title : tiêu đề của column
  const getColumnSearchProps = (dataIndex: TableLoaiHoGiaDinhColumnDataIndex, title: string): TableColumnType<TableLoaiHoGiaDinhColumnDataType> => ({
    /**
     *  filterDropdown: UI tuỳ chỉnh của ô filter (component render ra trong dropdown filter)
     * @param param0
     * @returns
     * dataIndex !== "trang_thai_ten"
     */
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? filterDropdownParams => (
            <TableFilterDropdown
              ref={refSearchInputTable}
              title={title}
              dataIndex={dataIndex}
              handleSearch={(selectedKeys, confirm, dataIndex) => handleSearch(selectedKeys, confirm, dataIndex)}
              handleReset={(clearFilters, confirm, dataIndex) => clearFilters && handleReset(clearFilters, confirm, dataIndex)}
              {...filterDropdownParams}
            />
          )
        : undefined,
    /**
     * onFilter: hàm filter dữ liệu tại client-side (bắt buộc phải khai báo để filter hoạt động)
     * do hàm onFilter luôn phải trả về boolean, nên thêm || false vào để k bị lỗi
     * @param value : giá trị filter người dùng nhập vào
     * @param record : từng bản ghi trong dataSource
     * @returns
     */
    onFilter: (value, record) => {
      return (
        record[dataIndex]
          ?.toString()
          .toLowerCase()
          .includes((value as string).toLowerCase()) || false
      );
    },
    /**
     * filterDropdownProps: thuộc tính tuì chỉnh hành vi dropdown
     */
    filterDropdownProps: {
      // onOpenChange: callback khi dropdown mở/ tắt thì sẽ focus vào input
      onOpenChange(open) {
        if (open) {
          setTimeout(() => refSearchInputTable.current?.select(), 100); // Focus/ select input khi mở filter
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiLoaiHoGiaDinhTable : undefined,
    render: (
      text,
      record,
      //  index
    ) => {
      if (dataIndex === "trang_thai_ten") {
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  return (
    <div id={ID_PAGE.DANH_MUC_BANG_MA_BENH} className="[&_.ant-space]:w-full">
      <Table<TableLoaiHoGiaDinhColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableListLoaiHoGiaDinh} //mảng dữ liệu record được hiển thị
        columns={
          tableLoaiHoGiaDinhColumn?.map(item => {
            //Nếu là cột stt thì không hiển thị search
            return {...item, ...(item.key === "sott" ? {} : getColumnSearchProps(item.key as keyof TableLoaiHoGiaDinhColumnDataType, item.title as string))};
          }) || []
        } //định nghĩa cột của table
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong,
          onChange: (page, pageSize) => {
            setFilterParams({...filterParams, trang: page, so_dong: pageSize});
          },
        }}
        title={renderHeaderTableQuanLyLoaiHoGiaDinh}
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, 
            onClick: async () => {
              
              if (record.key.toString().includes("empty")) {
                return;
              }
              
             
              
              const response = await getChiTietLoaiHoGiaDinh(record);
              
              
              if (response?.ma) {
                refModalChiTietLoaiHoGiaDinh.current?.open(response);
              } else {
                console.log("[LoaiHoGiaDinh] Không có response.ma, không mở modal");
              }
            }, // click row
          };
        }}
      />
      <ModalChiTietLoaiHoGiaDinh ref={refModalChiTietLoaiHoGiaDinh} listLoaiHoGiaDinh={listLoaiHoGiaDinh} />
    </div>
  );
}, isEqual);

QuanLyLoaiHoGiaDinhContent.displayName = "QuanLyLoaiHoGiaDinhContent";

export default QuanLyLoaiHoGiaDinhContent;

const antCls = ".ant";
const useStyle = createStyles(({css}) => {
  return {
    customTable: css`
      ${antCls}-table {
        ${antCls}-table-container {
          ${antCls}-table-body,
          ${antCls}-table-content {
            scrollbar-width: thin;
            scrollbar-color: #eaeaea transparent;
            scrollbar-gutter: stable;
          }
        }
      }
    `,
  };
});
