import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {
  ngayApDungColumns,
  //   cauHoiColumns,
  //   DataIndexCauHoi,
  //   DataIndexNgayApDung,
  //   FormThemNgayApDung,
  IModalChiTietNganSachHoTroRef,
  Props,
  TableNgayApDungDataType,
  FormThemNgayApDung,
  DataIndexNgayApDung,
  //   TableCauHoiDataType,
} from "./index.configs";
import {Col, Dropdown, Flex, Form, InputRef, Modal, Row, Space, Table, TableColumnType} from "antd";
import {useCauHinhNganSachHoTroContext} from "../index.context";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal, Highlighter, Popcomfirm, TableFilterDropdown} from "@src/components";
import {CloseOutlined, PlusCircleOutlined} from "@ant-design/icons";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {FilterDropdownProps} from "antd/es/table/interface";
import dayjs from "dayjs";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
// import {ModalThemCauHoi} from "./ModalThemCauHoi";

dayjs.extend(isSameOrBefore);
// const {ma_doi_tac_ql, ten, nv} = FormchiTietTinhThanh;

const ModalChiTietNganSachHoTroComponent = forwardRef<IModalChiTietNganSachHoTroRef, Props>(({}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDanhMucTinhThanh?: CommonExecute.Execute.IDanhMucTinhThanh) => {
      setIsOpen(true);
      //   if (dataDanhMucTinhThanh) setchiTietTinhThanh(dataDanhMucTinhThanh);
    },
    close: () => {
      setIsOpen(false);
      //   setchiTietTinhThanh(null);
      //   setDanhSachCauHoi([]);
    },
  }));
  //   const refModalThemCauHoi = useRef<IModalThemCauHoiRef>(null);
  const {ngay_ad} = FormThemNgayApDung;
  const [isOpen, setIsOpen] = useState(false);
  const [pageSize, setPageSize] = useState(10);
  const {
    loading,
    filterParams,
    danhSachNganSachHoTroNgayApDung,
    chiTietTinhThanh,
    // danhSachCauHoi,
    // selectedNgayApDung,
    // setFilterParams,
    // setSelectedCauHoiApDung,
    // onUpdateCauHoiApDung,
    // setDanhSachCauHoi,
    layDanhSachNganSachHoTroNgayApDung,
    // onDeleteCauHoiApDung,
    // onDeleteCauHoi,
    // layDanhSachCauHoi,
    // layChiTietCauHoi,
    // listNghiepVu,
    updateNganSachHoTroNgayApDung,
  } = useCauHinhNganSachHoTroContext();
  const [searchedColumn, setSearchedColumn] = useState("");
  const [searchText, setSearchText] = useState("");
  const searchInput = useRef<InputRef>(null);
  const [searchedColumnCH, setSearchedColumnCH] = useState("");
  const [searchTextCH, setSearchTextCH] = useState("");
  const searchInputCH = useRef<InputRef>(null);
  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [formThemNgayApDung] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [ngayAdMoiTao, setNgayAdMoiTao] = useState<string | null>(null);
  useEffect(() => {
    if (chiTietTinhThanh) {
      const arrFormData = [];
      for (const key in chiTietTinhThanh) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IDanhMucTinhThanh,
          value: chiTietTinhThanh[key as keyof CommonExecute.Execute.IDanhMucTinhThanh],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietTinhThanh, form]);
  useEffect(() => {
    if (chiTietTinhThanh?.ma) {
      layDanhSachNganSachHoTroNgayApDung({ma_tinh: chiTietTinhThanh.ma});
    }
  }, [chiTietTinhThanh]);

  //   useEffect(() => {
  //     if (selectedNgayApDung !== null) {
  //       setDisableSubmit(false);
  //     } else {
  //       setDisableSubmit(true);
  //     }
  //   }, [selectedNgayApDung]);
  // Tự động chọn dòng cuối cùng khi danh sách thay đổi
  //   useEffect(() => {
  //     if (danhSachNganSachHoTroNgayApDung.length > 0) {
  //       let selected;
  //       if (ngayAdMoiTao) {
  //         // Tìm ngày vừa tạo
  //         selected = danhSachNganSachHoTroNgayApDung.find(item => dayjs(item.ngay_ad, "DD/MM/YYYY").isSame(dayjs(ngayAdMoiTao, "DD/MM/YYYY")));
  //         setNgayAdMoiTao(null); // Reset lại sau khi đã select
  //       } else {
  //         // Tìm ngày gần hôm nay nhất (không vượt quá hôm nay)
  //         const today = dayjs();
  //         const validDates = danhSachNganSachHoTroNgayApDung.filter(item => dayjs(item.ngay_ad, "DD/MM/YYYY").isBefore(today) || dayjs(item.ngay_ad, "DD/MM/YYYY").isSame(today));

  //         if (validDates.length > 0) {
  //           selected = validDates.reduce((prev, curr) => {
  //             const prevDiff = today.diff(dayjs(prev.ngay_ad, "DD/MM/YYYY"), "day");
  //             const currDiff = today.diff(dayjs(curr.ngay_ad, "DD/MM/YYYY"), "day");
  //             return currDiff < prevDiff ? curr : prev;
  //           });
  //         } else {
  //           // Fallback: nếu không có ngày nào <= hôm nay, chọn ngày gần nhất
  //           selected = danhSachNganSachHoTroNgayApDung.reduce((prev, curr) => {
  //             const prevDiff = Math.abs(today.diff(dayjs(prev.ngay_ad, "DD/MM/YYYY"), "day"));
  //             const currDiff = Math.abs(today.diff(dayjs(curr.ngay_ad, "DD/MM/YYYY"), "day"));
  //             return currDiff < prevDiff ? curr : prev;
  //           });
  //         }
  //       }
  //       if (selected && selected.bt !== undefined && selected.bt !== null) {
  //         setSelectedCauHoiApDung(Number(selected.bt));
  //         layDanhSachCauHoi({bt_ap_dung: Number(selected.bt)});
  //       }
  //     } else {
  //       setSelectedCauHoiApDung(null);
  //       // setDanhSachCauHoi([]);
  //     }
  //   }, [danhSachNganSachHoTroNgayApDung]);
  const closeModal = () => {
    setIsOpen(false);
    // setchiTietTinhThanh(null);
    // setDanhSachCauHoi([]);

    form.resetFields();
    // setFilterParams(filterParams);
  };
  // Dữ liệu bảng câu hỏi áp dụng(bên trái)
  const dataTableListNgayApDung = useMemo<Array<TableNgayApDungDataType>>(() => {
    try {
      const mappedData = danhSachNganSachHoTroNgayApDung.map((item: any, index: number) => ({
        stt: item.sott ?? index + 1,
        ngay_ad: item.ngay_ad || "",
        ma_tinh: item.ma_tinh,
        key: index.toString(),
        hanh_dong: () => renderDeleteButton(item.bt),
      }));
      const arrEmptyRow = fillRowTableEmpty(mappedData.length, pageSize);
      return [...mappedData, ...arrEmptyRow];
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng ngày áp dụng:", error);
      return [];
    }
  }, [danhSachNganSachHoTroNgayApDung, pageSize]);
  //   const dataTableListCauHoi = useMemo<Array<TableCauHoiDataType>>(() => {
  //     try {
  //       const mappedData = danhSachCauHoi.map((item: any, index: number) => ({
  //         stt: item.sott ?? index + 1,
  //         ngay_ad: item.ngay_ad || "",
  //         bt_ap_dung: item.bt_ap_dung,
  //         ma: item.ma,
  //         ten: item.ten,
  //         kieu_chon: item.kieu_chon,
  //         bat_buoc: item.bat_buoc,
  //         do_rong: item.do_rong,
  //         ngay_tao: item.ngay_tao,
  //         nguoi_tao: item.nguoi_tao,
  //         ngay_cap_nhat: item.ngay_cap_nhat,
  //         nguoi_cap_nhat: item.nguoi_cap_nhat,
  //         key: index.toString(),
  //         hanh_dong: () => renderDeleteButtonCH(item.ma),
  //       }));

  //       const arrEmptyRow = fillRowTableEmpty(mappedData.length, pageSize);
  //       return [...mappedData, ...arrEmptyRow];
  //     } catch (error) {
  //       console.error("Lỗi khi xử lý dữ liệu bảng ngày áp dụng:", error);
  //       return [];
  //     }
  //   }, [danhSachCauHoi, pageSize]);
  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexNgayApDung) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexNgayApDung) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  const handleSubmit = async () => {
    try {
      const values = await formThemNgayApDung.validateFields();

      if (!values.ngay_ad) {
        console.log("Vui lòng chọn ngày áp dụng");
        return;
      }
      console.log("chiTietTinhThanh", chiTietTinhThanh);
      await updateNganSachHoTroNgayApDung({
        ma_tinh: chiTietTinhThanh?.ma,
        ngay_ad: Number(dayjs(values.ngay_ad).format("YYYYMMDD")),
      });
      setDropdownOpen(false); // Đóng dropdown sau khi lưu thành công
      formThemNgayApDung.resetFields();
      setNgayAdMoiTao(values.ngay_ad); // Lưu lại ngày vừa tạo

      if (chiTietTinhThanh?.ma) {
        layDanhSachNganSachHoTroNgayApDung({ma_tinh: chiTietTinhThanh.ma});
      }
    } catch (error) {
      console.log("Lỗi khi submit:", error);
      // Không đóng dropdown nếu có lỗi để người dùng có thể sửa
    }
  };
  const handleDelete = async (bt: number) => {
    try {
      await onDeleteCauHoiApDung({
        bt: Number(bt),
      });

      // Reset selection sau khi xóa
      setSelectedCauHoiApDung(null);
      //  setDanhSachCauHinhPhanCapPheDuyetCT([]);

      // Refresh danh sách
      if (chiTietTinhThanh?.ma && chiTietTinhThanh.nv && chiTietTinhThanh.ma_doi_tac_ql) {
        laydanhSachNganSachHoTroNgayApDung({ma_sp: chiTietTinhThanh.ma, nv: chiTietTinhThanh.nv, ma_doi_tac_ql: chiTietTinhThanh.ma_doi_tac_ql});
      }
    } catch (error) {
      console.log("Lỗi khi xóa ngày áp dụng:", error);
    }
  };

  const getColumnSearchCauHoiApDungProps = (dataIndex: DataIndexNgayApDung, title: string): TableColumnType<DataIndexNgayApDung> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
      <TableFilterDropdown
        ref={searchInput}
        title={title}
        selectedKeys={selectedKeys}
        dataIndex={dataIndex}
        setSelectedKeys={setSelectedKeys}
        handleSearch={handleSearch}
        confirm={confirm}
        clearFilters={clearFilters}
        handleReset={handleReset}
      />
    ),
    // filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex as keyof DataIndexNgayApDung]
        ? record[dataIndex as keyof DataIndexNgayApDung]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    // filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiNhomTable : undefined,
    render: (text, record, index) => {
      //   if (dataIndex === "trang_thai_ten") {
      //     // Xác định màu dựa vào text (trạng thái hiển thị)
      //     const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
      //     if (record.key.toString().includes("empty")) return "";
      //     return <Tag color={color}>{text}</Tag>;
      //   }
      // if (record.key.toString().includes("empty")) return <div style={{height: 20}} />;

      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Button type="link" className="h-auto p-0" style={{color: "transparent"}} icon={<CloseOutlined style={{visibility: "hidden"}} />}></Button>
      );
    },
  });

  const renderDeleteButton = (bt?: number) => {
    if (!bt) return null;
    return (
      <div>
        <Popcomfirm
          title="Thông báo"
          onConfirm={() => handleDelete(bt)}
          htmlType="button"
          okText="Xóa"
          description="Bạn có chắc muốn xóa ngày áp dụng?"
          buttonTitle={""}
          buttonColor="red"
          okButtonProps={{
            style: {
              backgroundColor: "white",
              borderColor: "red",
              color: "red",
            },
          }}
          style={{width: "fit-content"}}
          variant="text"
          className="h-auto"
          icon={<CloseOutlined />}
          buttonIcon
        />
      </div>
    );
  };
  const renderDeleteButtonCH = (ma?: string) => {
    if (!ma) return null;

    return (
      <div
        onClick={e => {
          e.stopPropagation();
        }}>
        <Popcomfirm
          title="Thông báo"
          onConfirm={() => handleDeleteCH(ma)}
          htmlType="button"
          okText="Xóa"
          description="Bạn có chắc muốn xóa câu hỏi?"
          buttonTitle={""}
          buttonColor="red"
          okButtonProps={{
            style: {
              backgroundColor: "white",
              borderColor: "red",
              color: "red",
            },
          }}
          style={{width: "fit-content"}}
          variant="text"
          className="h-auto"
          icon={<CloseOutlined />}
          buttonIcon
        />
      </div>
    );
  };

  const renderTableCauHoiApDungFooter = () => {
    return (
      <div className="">
        <Form.Item className="" style={{marginTop: 16, marginRight: 8, marginBottom: 0}}>
          {/* <Space direction="vertical" > */}
          <Space className="">
            <Dropdown
              className=""
              open={dropdownOpen}
              onOpenChange={setDropdownOpen}
              trigger={["click"]}
              getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
              dropdownRender={() => (
                <div style={{padding: 8, display: "flex"}}>
                  <Form id="formThemNgayApDung" form={formThemNgayApDung} layout="vertical">
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "flex-end"}}>
                      {renderFormInputColum({...ngay_ad}, 24)}
                      {/* <Form.Item> */}
                      <Button type="primary" onClick={handleSubmit} style={{marginLeft: 8, marginBottom: 8}}>
                        Áp dụng
                      </Button>
                      {/* </Form.Item> */}
                    </div>
                  </Form>
                </div>
              )}
              placement="topRight">
              <Button className="w-full" type="primary" icon={<PlusCircleOutlined />} onClick={() => setDropdownOpen(true)}>
                Thêm ngày áp dụng
              </Button>
            </Dropdown>
          </Space>
          {/* </Space> */}
        </Form.Item>
      </div>
    );
  };

  const renderTableCauHoiApDung = () => {
    return (
      <Table<TableNgayApDungDataType>
        className="table-vai-tro no-header-border-radius"
        {...defaultTableProps}
        style={{cursor: "pointer"}}
        onRow={(record, rowIndex) => {
          return {
            style: {
              cursor: "pointer",
              //   background: record.bt === selectedNgayApDung ? "#96BF49" : undefined, // Làm nổi bật hàng được chọn
            },
            // onClick: () => handleCauHoiApDungRowClick(record), // Xử lý nhấp vào hàng
          };
        }}
        title={null}
        pagination={false}
        columns={(ngayApDungColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchCauHoiApDungProps(item.key as keyof TableNgayApDungDataType, item.title) : {}),
          };
        })}
        dataSource={dataTableListNgayApDung}
        bordered
        scroll={dataTableListNgayApDung.length > pageSize ? {y: 285} : undefined}
      />
    );
  };
  //   const renderTableCauHoiFooter = () => {
  //     return (
  //       <Form.Item style={{marginTop: 16, marginBottom: 0, textAlign: "end"}}>
  //         <Button className="" type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalThemCauHoi.current?.open()} loading={loading} disabled={disableSubmit}>
  //           Thêm câu hỏi
  //         </Button>
  //       </Form.Item>
  //     );
  //   };
  //   const renderTableCauHoi = () => {
  //     return (
  //       <Table<TableCauHoiDataType>
  //         className="table-nhom no-header-border-radius"
  //         {...defaultTableProps}
  //         style={{cursor: "pointer"}}
  //         onRow={(record, rowIndex) => {
  //           return {
  //             style: {cursor: "pointer"},
  //             onClick: async event => {
  //               if (record.key.toString().includes("empty")) return;
  //               const response: CommonExecute.Execute.ICauHoi | null = await layChiTietCauHoi({ma: record.ma, bt_ap_dung: Number(selectedNgayApDung)});
  //               if (response) refModalThemCauHoi.current?.open(response);
  //             }, // click row
  //           };
  //         }}
  //         title={null}
  //         pagination={false}
  //         columns={(cauHoiColumns || []).map(item => {
  //           // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
  //           return {
  //             ...item,
  //             ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchCauHoiProps(item.key as keyof TableCauHoiDataType, item.title) : {}),
  //           };
  //         })}
  //         dataSource={dataTableListCauHoi}
  //         bordered
  //         scroll={dataTableListCauHoi.length > pageSize ? {y: 285} : undefined}
  //       />
  //     );
  //   };
  const renderTable = () => {
    return (
      <Row>
        <Col span={7} style={{paddingRight: 16}}>
          {renderTableCauHoiApDung()}
          {renderTableCauHoiApDungFooter()}
        </Col>
        <Col span={17}></Col>
      </Row>
    );
  };
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={chiTietTinhThanh ? `Chi tiết ngân sách hỗ trợ của ${chiTietTinhThanh.ten}` : ""} trang_thai={chiTietTinhThanh?.trang_thai} />}
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width="100vw"
        // styles={{
        //   body: {
        //     height: "60vh",
        //   },
        // }}
        footer={null}
        className="modal-them-cau-hoi-ct [&_.ant-space]:w-full">
        {renderTable()}
      </Modal>
    </Flex>
  );
});
ModalChiTietNganSachHoTroComponent.displayName = "ModalChiTietNganSachHoTroComponent";
export const ModalChiTietNganSachHoTro = memo(ModalChiTietNganSachHoTroComponent, isEqual);
